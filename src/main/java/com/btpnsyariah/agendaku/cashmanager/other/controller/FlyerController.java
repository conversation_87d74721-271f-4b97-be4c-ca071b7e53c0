package com.btpnsyariah.agendaku.cashmanager.other.controller;

import com.btpnsyariah.agendaku.cashmanager.other.model.FlyerDTO;
import com.btpnsyariah.agendaku.cashmanager.other.model.NationalHolidayDTO;
import com.btpnsyariah.agendaku.cashmanager.other.service.FlyerService;
import com.btpnsyariah.agendaku.cashmanager.util.DataFoundException;
import com.btpnsyariah.agendaku.cashmanager.util.MinioService;
import com.btpnsyariah.agendaku.cashmanager.util.Utility;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;

@RestController
@RequestMapping("/flyer")
public class FlyerController {

    @Autowired
    FlyerService flyerService;
    @Autowired
    MinioService minioService;
    @Autowired
    ObjectMapper objectMapper;

    @Operation(summary = "Submit Flyer")
    @CrossOrigin(origins = {"https://cash-management-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://cash-management-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000", "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com"})
    @PostMapping(value = "/save", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity persistFlyer(@RequestHeader("Access-Token") String token,
                                       @RequestParam("file") MultipartFile file,
                                       @RequestParam("data") String jsonData) throws IOException {
        try {
            FlyerDTO flyerData = objectMapper.readValue(jsonData, FlyerDTO.class);
            flyerService.persistFlyer(file, flyerData, token);
            return new ResponseEntity<>("Flyer persisted!", HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    @Operation(summary = "Get All Flyers")
    @GetMapping()
    @CrossOrigin(origins = {"https://cash-management-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://cash-management-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000", "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com"})
    public ResponseEntity getFlyers(@RequestParam(required = false, value = "version") String version,
                                              @RequestParam(required = false, value = "startDate") String startDate,
                                              @RequestParam(required = false, value = "endDate") String endDate,
                                              Pageable pageable){
        try {
            return new ResponseEntity<>(flyerService.getAllFlyers(version,startDate,endDate, pageable), HttpStatus.OK);
        } catch (Exception e) {
            return new ResponseEntity(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Operation(summary = "Download Flyer")
    @CrossOrigin(origins = {"https://cash-management-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://cash-management-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000", "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com"})
    @GetMapping("/download")
    public ResponseEntity downloadFlyer(@RequestParam(required = false, value = "fileName") String fileName,
                                       @RequestHeader("Access-Token") String token){
        try {
            // Extract filename and extension from the fileName parameter
            String actualFileName = Utility.getFileNameFromPath(fileName);
            String fileExtension = Utility.getFileNameExtension(fileName);

            InputStream inputStream = minioService.getFile(fileName, token);

            // Determine content type based on file extension
            MediaType contentType = Utility.getMediaTypeFromExtension(fileExtension);

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + actualFileName + "\"")
                    .contentType(contentType)
                    .body(new InputStreamResource(inputStream));
        } catch (Exception e) {
            return new ResponseEntity(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @DeleteMapping("/delete")
    @CrossOrigin(origins = {"https://cash-management-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://cash-management-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
            "https://core-agendaku-south.apps.south.syariahbtpn.com",
            "https://core-agendaku-north.apps.north.syariahbtpn.com",
            "http://localhost:3000", "http://localhost:3001",
            "https://agendaku.apps.btpnsyariah.com"})
    public ResponseEntity deleteNationalHoliday(@RequestParam("id") Long id){
        try {
            flyerService.deleteFlyer(id);
            return new ResponseEntity<>(String.format("Flyer with id %s deleted successfully!", id), HttpStatus.OK);
        } catch (DataFoundException e) {
            return new ResponseEntity(e.getMessage(), HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            return new ResponseEntity(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

//    @PutMapping("/update")
//    @CrossOrigin(origins = {"https://cash-management-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
//            "https://cash-management-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
//            "https://core-agendaku-dirty.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
//            "https://core-agendaku-beta.apps.btpnsdev1.c3vu.p1.openshiftapps.com",
//            "https://core-agendaku-south.apps.south.syariahbtpn.com",
//            "https://core-agendaku-north.apps.north.syariahbtpn.com",
//            "http://localhost:3000", "http://localhost:3001",
//            "https://agendaku.apps.btpnsyariah.com"})
//    public ResponseEntity updateNationalHoliday(@RequestParam("id") Long id, @RequestBody NationalHolidayDTO nationalHolidayDTO, @RequestHeader("UserId") String userId){
//        try {
//            flyerService.updateNationalHoliday(id, nationalHolidayDTO, userId);
//            return new ResponseEntity<>(String.format("National holiday with id %s updated successfully!", id), HttpStatus.OK);
//        } catch (DataFoundException e) {
//            return new ResponseEntity(e.getMessage(), HttpStatus.NOT_FOUND);
//        } catch (Exception e) {
//            return new ResponseEntity(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
//        }
//    }
}
