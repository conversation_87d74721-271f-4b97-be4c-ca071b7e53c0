package com.btpnsyariah.agendaku.cashmanager.other.repository;

import com.btpnsyariah.agendaku.cashmanager.other.model.FlyerEntity;
import com.btpnsyariah.agendaku.cashmanager.personas.CashUserPersonasEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface FlyerRepository extends JpaRepository<FlyerEntity, Long> {
    @Query(nativeQuery = true, value = "SELECT * FROM FlyerConfig WHERE CONVERT(DATE, DtActiveFrom) <= CONVERT(DATE, :endDate)\n" +
            "  AND CONVERT(DATE, DtActiveTo) >= CONVERT(DATE, :startDate) AND (:version IS NULL OR ApkVersion = :version) AND IsActive = 1")
    List<FlyerEntity> findAllFlyerStartingInAndEndingInWithOptionalVersion(Date startDate, Date endDate, String version);

    @Query("SELECT * FROM FlyerConfig WHERE :version IS NULL OR apkVersion = :version AND IsActive = 1")
    List<FlyerEntity> findAllByApkVersionOrAll(String version);

}
